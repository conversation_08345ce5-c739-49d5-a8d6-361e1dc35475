"""
Example usage of dashcam 3D road reconstruction
"""

import os
import sys
import json
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from dashcam_inference import process_dashcam_video
from src.camera_calibration import create_calibration_config, estimate_camera_params_from_video


def example_basic_processing():
    """Basic example of processing a dashcam video"""
    
    print("=== Basic Dashcam Processing Example ===")
    
    # Example video path (replace with your video)
    video_path = "sample_dashcam.mp4"
    
    if not os.path.exists(video_path):
        print(f"Please provide a dashcam video file at: {video_path}")
        print("Or modify the video_path variable in this script")
        return
    
    # Process the video
    try:
        results = process_dashcam_video(
            video_path=video_path,
            output_dir="example_output",
            frame_skip=15,  # Process every 15th frame
            max_frames=50   # Process up to 50 frames
        )
        
        print(f"\n✓ Successfully processed {len(results)} frames!")
        print("Check the 'example_output' directory for results")
        
    except Exception as e:
        print(f"Error: {e}")


def example_with_camera_calibration():
    """Example with custom camera calibration"""
    
    print("\n=== Camera Calibration Example ===")
    
    video_path = "sample_dashcam.mp4"
    
    if not os.path.exists(video_path):
        print(f"Video file not found: {video_path}")
        return
    
    # Create camera configuration
    config_path = create_calibration_config(video_path, "example_camera_config.json")
    
    # You can modify the generated config file to improve accuracy
    print(f"Camera configuration created: {config_path}")
    print("You can edit this file to provide more accurate camera parameters")
    
    # Process with custom camera config
    try:
        results = process_dashcam_video(
            video_path=video_path,
            output_dir="example_output_calibrated",
            camera_config=config_path,
            frame_skip=10,
            max_frames=30
        )
        
        print(f"\n✓ Processed {len(results)} frames with custom camera config!")
        
    except Exception as e:
        print(f"Error: {e}")


def example_analyze_results():
    """Example of analyzing processing results"""
    
    print("\n=== Results Analysis Example ===")
    
    results_dir = "example_output"
    summary_file = os.path.join(results_dir, "processing_summary.json")
    
    if not os.path.exists(summary_file):
        print(f"No results found at {results_dir}")
        print("Run the basic processing example first")
        return
    
    # Load processing summary
    with open(summary_file, 'r') as f:
        summary = json.load(f)
    
    print(f"Processing Summary:")
    print(f"  Total frames processed: {summary['total_frames']}")
    print(f"  Grid configuration: {summary['grid_config']}")
    
    # Check for 3D model files
    ply_file = os.path.join(results_dir, "road_model.ply")
    obj_file = os.path.join(results_dir, "road_model.obj")
    
    if os.path.exists(ply_file):
        size_mb = os.path.getsize(ply_file) / (1024 * 1024)
        print(f"  3D model (PLY): {ply_file} ({size_mb:.1f} MB)")
    
    if os.path.exists(obj_file):
        size_mb = os.path.getsize(obj_file) / (1024 * 1024)
        print(f"  3D model (OBJ): {obj_file} ({size_mb:.1f} MB)")
    
    # List BEV map files
    bev_dir = os.path.join(results_dir, "bev_maps")
    if os.path.exists(bev_dir):
        bev_files = [f for f in os.listdir(bev_dir) if f.endswith('.png')]
        print(f"  BEV visualizations: {len(bev_files)} files in {bev_dir}")


def example_custom_processing():
    """Example with custom processing parameters"""
    
    print("\n=== Custom Processing Example ===")
    
    video_path = "sample_dashcam.mp4"
    
    if not os.path.exists(video_path):
        print(f"Video file not found: {video_path}")
        return
    
    # Custom grid configuration for different scene
    from dashcam_inference import setup_model_config
    
    grid_conf, data_aug_conf = setup_model_config()
    
    # Modify for highway scenario (longer range)
    grid_conf['ybound'] = [0.0, 100.0, 0.5]  # Extend forward range to 100m
    grid_conf['xbound'] = [-30.0, 30.0, 0.5]  # Wider side range
    
    print("Using custom grid configuration:")
    print(f"  Forward range: {grid_conf['ybound'][0]} to {grid_conf['ybound'][1]} meters")
    print(f"  Side range: {grid_conf['xbound'][0]} to {grid_conf['xbound'][1]} meters")
    
    # Note: For full custom processing, you'd need to modify the main function
    # This is just to show how you can customize the configuration


def create_sample_video_info():
    """Create information about sample video requirements"""
    
    print("\n=== Sample Video Requirements ===")
    print("For best results, your dashcam video should have:")
    print("  • Resolution: 1280x720 or higher")
    print("  • Frame rate: 30 FPS or higher")
    print("  • Good lighting conditions")
    print("  • Stable camera mounting")
    print("  • Clear view of the road")
    print("  • Minimal motion blur")
    print("\nSupported formats: MP4, AVI, MOV, MKV")
    print("Place your video file as 'sample_dashcam.mp4' in this directory")


def main():
    """Run all examples"""
    
    print("Dashcam 3D Road Reconstruction - Example Usage")
    print("=" * 50)
    
    # Check if we have a sample video
    if not os.path.exists("sample_dashcam.mp4"):
        create_sample_video_info()
        return
    
    # Run examples
    example_basic_processing()
    example_with_camera_calibration()
    example_analyze_results()
    example_custom_processing()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nNext steps:")
    print("1. Check the output directories for results")
    print("2. Open the 3D model files in a 3D viewer (e.g., MeshLab, Blender)")
    print("3. Experiment with different processing parameters")
    print("4. Try camera calibration for better accuracy")


if __name__ == "__main__":
    main()
