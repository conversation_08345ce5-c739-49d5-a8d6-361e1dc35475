"""
Camera calibration utilities for dashcam footage processing
"""

import numpy as np
import cv2
import json
import os
from glob import glob
from typing import Dict, List, Tuple, Optional


class CameraCalibrator:
    """Camera calibration utility for dashcam footage"""
    
    def __init__(self):
        self.camera_matrix = None
        self.dist_coeffs = None
        self.rvecs = None
        self.tvecs = None
        
    def calibrate_from_chessboard(self, images_path: str, pattern_size: Tuple[int, int] = (9, 6),
                                 square_size: float = 1.0) -> Dict:
        """
        Calibrate camera using chessboard pattern images
        
        Args:
            images_path: Path to directory containing chessboard images
            pattern_size: Number of inner corners (width, height)
            square_size: Size of chessboard squares in real units
            
        Returns:
            Dictionary containing calibration parameters
        """
        # Prepare object points
        objp = np.zeros((pattern_size[0] * pattern_size[1], 3), np.float32)
        objp[:, :2] = np.mgrid[0:pattern_size[0], 0:pattern_size[1]].T.reshape(-1, 2)
        objp *= square_size
        
        # Arrays to store object points and image points
        objpoints = []  # 3d points in real world space
        imgpoints = []  # 2d points in image plane
        
        # Get list of calibration images
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_files.extend(glob.glob(os.path.join(images_path, ext)))
            image_files.extend(glob.glob(os.path.join(images_path, ext.upper())))
        
        if not image_files:
            raise ValueError(f"No calibration images found in {images_path}")
        
        print(f"Found {len(image_files)} calibration images")
        
        successful_detections = 0
        for fname in image_files:
            img = cv2.imread(fname)
            if img is None:
                continue
                
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Find chessboard corners
            ret, corners = cv2.findChessboardCorners(gray, pattern_size, None)
            
            if ret:
                objpoints.append(objp)
                
                # Refine corner positions
                corners2 = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1),
                                          (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
                imgpoints.append(corners2)
                successful_detections += 1
                print(f"✓ Detected chessboard in {os.path.basename(fname)}")
            else:
                print(f"✗ Failed to detect chessboard in {os.path.basename(fname)}")
        
        if successful_detections < 10:
            print(f"Warning: Only {successful_detections} successful detections. Recommend at least 10 for good calibration.")
        
        # Perform camera calibration
        img_shape = gray.shape[::-1]  # (width, height)
        ret, mtx, dist, rvecs, tvecs = cv2.calibrateCamera(objpoints, imgpoints, img_shape, None, None)
        
        if not ret:
            raise RuntimeError("Camera calibration failed")
        
        # Store calibration results
        self.camera_matrix = mtx
        self.dist_coeffs = dist
        self.rvecs = rvecs
        self.tvecs = tvecs
        
        # Calculate reprojection error
        total_error = 0
        for i in range(len(objpoints)):
            imgpoints2, _ = cv2.projectPoints(objpoints[i], rvecs[i], tvecs[i], mtx, dist)
            error = cv2.norm(imgpoints[i], imgpoints2, cv2.NORM_L2) / len(imgpoints2)
            total_error += error
        
        mean_error = total_error / len(objpoints)
        
        calibration_result = {
            'camera_matrix': mtx.tolist(),
            'distortion_coefficients': dist.tolist(),
            'image_size': img_shape,
            'reprojection_error': mean_error,
            'successful_detections': successful_detections,
            'total_images': len(image_files)
        }
        
        print(f"Camera calibration completed!")
        print(f"Reprojection error: {mean_error:.4f} pixels")
        print(f"Successful detections: {successful_detections}/{len(image_files)}")
        
        return calibration_result
    
    def save_calibration(self, filepath: str, calibration_data: Dict):
        """Save calibration data to JSON file"""
        with open(filepath, 'w') as f:
            json.dump(calibration_data, f, indent=2)
        print(f"Calibration saved to {filepath}")
    
    def load_calibration(self, filepath: str) -> Dict:
        """Load calibration data from JSON file"""
        with open(filepath, 'r') as f:
            calibration_data = json.load(f)
        
        self.camera_matrix = np.array(calibration_data['camera_matrix'])
        self.dist_coeffs = np.array(calibration_data['distortion_coefficients'])
        
        return calibration_data


def get_default_camera_params(image_width: int = 1280, image_height: int = 720) -> Dict:
    """
    Get default camera parameters for typical dashcam
    
    Args:
        image_width: Image width in pixels
        image_height: Image height in pixels
        
    Returns:
        Dictionary with camera parameters for lift-splat-shoot model
    """
    # Estimate focal length (typical for dashcam with ~60-70 degree FOV)
    focal_length = image_width * 0.7  # Approximate focal length
    
    # Principal point at image center
    cx = image_width / 2.0
    cy = image_height / 2.0
    
    return {
        'intrinsic': [
            [focal_length, 0.0, cx],
            [0.0, focal_length, cy],
            [0.0, 0.0, 1.0]
        ],
        'rotation': [
            [1.0, 0.0, 0.0],  # Identity rotation (camera facing forward)
            [0.0, 1.0, 0.0],
            [0.0, 0.0, 1.0]
        ],
        'translation': [0.0, 0.0, 1.5]  # Camera height above ground (meters)
    }


def estimate_camera_params_from_video(video_path: str, sample_frames: int = 10) -> Dict:
    """
    Estimate camera parameters from video file
    
    Args:
        video_path: Path to video file
        sample_frames: Number of frames to sample for estimation
        
    Returns:
        Dictionary with estimated camera parameters
    """
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        raise ValueError(f"Could not open video file: {video_path}")
    
    # Get video properties
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    cap.release()
    
    print(f"Video properties: {width}x{height}, {total_frames} frames")
    
    # Use default parameters based on video resolution
    params = get_default_camera_params(width, height)
    
    print("Using default camera parameters (for better results, perform proper calibration)")
    print(f"Estimated focal length: {params['intrinsic'][0][0]:.1f}")
    print(f"Principal point: ({params['intrinsic'][0][2]:.1f}, {params['intrinsic'][1][2]:.1f})")
    
    return params


def create_calibration_config(video_path: str, output_path: str = None) -> str:
    """
    Create a calibration configuration file for a video
    
    Args:
        video_path: Path to dashcam video
        output_path: Path to save configuration file
        
    Returns:
        Path to created configuration file
    """
    if output_path is None:
        base_name = os.path.splitext(os.path.basename(video_path))[0]
        output_path = f"{base_name}_camera_config.json"
    
    # Estimate parameters from video
    params = estimate_camera_params_from_video(video_path)
    
    # Create configuration
    config = {
        'video_path': video_path,
        'camera_parameters': params,
        'calibration_method': 'estimated',
        'notes': 'Default parameters estimated from video resolution. For better accuracy, perform chessboard calibration.'
    }
    
    # Save configuration
    with open(output_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Camera configuration saved to: {output_path}")
    return output_path
