"""
Dashcam 3D Road Reconstruction using Lift-Splat-Shoot
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import cv2
import json
import argparse
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.models import compile_model
from src.data import compile_dashcam_data
from src.camera_calibration import estimate_camera_params_from_video, create_calibration_config
from src.visualization_3d import create_road_model_from_results


def setup_model_config():
    """Setup default model configuration for dashcam processing"""

    # Grid configuration - using exact nuScenes settings to avoid tensor mismatch
    grid_conf = {
        'xbound': [-50.0, 50.0, 0.5],    # Left-right bounds (meters), resolution
        'ybound': [-50.0, 50.0, 0.5],    # Forward bounds (meters), resolution
        'zbound': [-10.0, 10.0, 20.0],   # Height bounds (meters), resolution
        'dbound': [4.0, 45.0, 1.0],      # Depth bounds for frustum (meters), resolution
    }

    # Data augmentation configuration - exact nuScenes settings
    data_aug_conf = {
        'resize_lim': (0.193, 0.225),    # Resize range
        'final_dim': (128, 352),         # Final image dimensions
        'rot_lim': (-5.4, 5.4),          # Rotation limits (degrees)
        'H': 900, 'W': 1600,             # Original image dimensions (will be updated based on video)
        'rand_flip': False,              # No random flip for inference
        'bot_pct_lim': (0.0, 0.22),      # Bottom crop percentage
        'cams': ['DASHCAM'],             # Single camera
        'Ncams': 1,                      # Number of cameras
    }
    
    return grid_conf, data_aug_conf


def load_pretrained_model(model_path, grid_conf, data_aug_conf, device):
    """Load pretrained lift-splat-shoot model"""
    
    # Create model
    model = compile_model(grid_conf, data_aug_conf, outC=1)
    
    if model_path and os.path.exists(model_path):
        print(f"Loading pretrained model from {model_path}")
        try:
            # Load state dict
            state_dict = torch.load(model_path, map_location=device)
            model.load_state_dict(state_dict)
            print("✓ Pretrained model loaded successfully")
        except Exception as e:
            print(f"Warning: Could not load pretrained model: {e}")
            print("Using randomly initialized model")
    else:
        print("No pretrained model provided. Using randomly initialized model.")
        print("Note: For best results, download a pretrained model from the original repository.")
    
    model.to(device)
    model.eval()
    
    return model


def process_dashcam_video(video_path, model_path=None, output_dir="output", 
                         camera_config=None, frame_skip=10, max_frames=100):
    """
    Process dashcam video to generate 3D road reconstruction
    
    Args:
        video_path: Path to dashcam video
        model_path: Path to pretrained model (optional)
        output_dir: Directory to save outputs
        camera_config: Path to camera configuration file
        frame_skip: Process every N-th frame
        max_frames: Maximum number of frames to process
    """
    
    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get video properties and setup configuration
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise ValueError(f"Could not open video: {video_path}")
    
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    cap.release()
    
    print(f"Video info: {width}x{height}, {total_frames} frames, {fps:.1f} FPS")
    
    # Setup model configuration
    grid_conf, data_aug_conf = setup_model_config()
    
    # Update data augmentation config with actual video dimensions
    data_aug_conf['H'] = height
    data_aug_conf['W'] = width
    
    # Load or create camera configuration
    if camera_config and os.path.exists(camera_config):
        print(f"Loading camera configuration from {camera_config}")
        with open(camera_config, 'r') as f:
            config = json.load(f)
        camera_params = config['camera_parameters']
    else:
        print("Estimating camera parameters from video...")
        camera_params = estimate_camera_params_from_video(video_path)
        
        # Save estimated parameters
        config_path = os.path.join(output_dir, "camera_config.json")
        create_calibration_config(video_path, config_path)
    
    # Load model
    model = load_pretrained_model(model_path, grid_conf, data_aug_conf, device)
    
    # Create data loader
    print("Creating data loader...")
    dataloader = compile_dashcam_data(
        video_path=video_path,
        data_aug_conf=data_aug_conf,
        grid_conf=grid_conf,
        bsz=1,
        nworkers=0,
        camera_params=camera_params,
        frame_skip=frame_skip
    )
    
    print(f"Processing {len(dataloader)} frames...")
    
    # Process frames
    results = []
    processed_count = 0
    
    with torch.no_grad():
        for batch_idx, (imgs, rots, trans, intrins, post_rots, post_trans) in enumerate(dataloader):
            if processed_count >= max_frames:
                break
                
            # Move to device
            imgs = imgs.to(device)
            rots = rots.to(device)
            trans = trans.to(device)
            intrins = intrins.to(device)
            post_rots = post_rots.to(device)
            post_trans = post_trans.to(device)
            
            # Forward pass
            output = model(imgs, rots, trans, intrins, post_rots, post_trans)
            
            # Convert to numpy for visualization
            bev_output = output.cpu().numpy()[0, 0]  # First batch, first channel
            
            results.append({
                'frame_idx': batch_idx * frame_skip,
                'bev_output': bev_output,
                'timestamp': batch_idx * frame_skip / fps
            })
            
            processed_count += 1
            
            if processed_count % 10 == 0:
                print(f"Processed {processed_count}/{min(max_frames, len(dataloader))} frames")
    
    print(f"✓ Processing complete! Generated {len(results)} BEV maps")
    
    # Save results
    save_results(results, output_dir, grid_conf)

    # Create 3D model with lower threshold for better results
    print("\nCreating 3D road model...")
    create_road_model_from_results(results, grid_conf, output_dir, threshold=0.1)

    return results


def save_results(results, output_dir, grid_conf):
    """Save processing results"""
    
    print("Saving results...")
    
    # Create subdirectories
    bev_dir = os.path.join(output_dir, "bev_maps")
    os.makedirs(bev_dir, exist_ok=True)
    
    # Save individual BEV maps
    for i, result in enumerate(results):
        bev_map = result['bev_output']
        
        # Save as image
        plt.figure(figsize=(10, 10))
        plt.imshow(bev_map, cmap='viridis', origin='lower')
        plt.title(f"BEV Map - Frame {result['frame_idx']} (t={result['timestamp']:.1f}s)")
        plt.xlabel("X (meters)")
        plt.ylabel("Y (meters)")
        
        # Add grid bounds as labels
        x_min, x_max = grid_conf['xbound'][0], grid_conf['xbound'][1]
        y_min, y_max = grid_conf['ybound'][0], grid_conf['ybound'][1]
        
        plt.xlim(0, bev_map.shape[1])
        plt.ylim(0, bev_map.shape[0])
        
        # Set tick labels to show real-world coordinates
        x_ticks = np.linspace(0, bev_map.shape[1], 5)
        y_ticks = np.linspace(0, bev_map.shape[0], 5)
        x_labels = np.linspace(x_min, x_max, 5)
        y_labels = np.linspace(y_min, y_max, 5)
        
        plt.xticks(x_ticks, [f"{x:.1f}" for x in x_labels])
        plt.yticks(y_ticks, [f"{y:.1f}" for y in y_labels])
        
        plt.colorbar(label="Occupancy Probability")
        plt.tight_layout()
        
        # Save plot
        plt.savefig(os.path.join(bev_dir, f"bev_frame_{i:04d}.png"), dpi=150, bbox_inches='tight')
        plt.close()
        
        # Save raw data
        np.save(os.path.join(bev_dir, f"bev_frame_{i:04d}.npy"), bev_map)
    
    # Save summary
    summary = {
        'total_frames': len(results),
        'grid_config': grid_conf,
        'frame_info': [{'frame_idx': r['frame_idx'], 'timestamp': r['timestamp']} for r in results]
    }
    
    with open(os.path.join(output_dir, "processing_summary.json"), 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"✓ Results saved to {output_dir}")
    print(f"  - BEV maps: {bev_dir}")
    print(f"  - Summary: {os.path.join(output_dir, 'processing_summary.json')}")


def main():
    parser = argparse.ArgumentParser(description="Process dashcam video for 3D road reconstruction")
    parser.add_argument("video_path", help="Path to dashcam video file")
    parser.add_argument("--model", help="Path to pretrained model file")
    parser.add_argument("--output", default="output", help="Output directory")
    parser.add_argument("--camera-config", help="Path to camera configuration file")
    parser.add_argument("--frame-skip", type=int, default=10, help="Process every N-th frame")
    parser.add_argument("--max-frames", type=int, default=100, help="Maximum frames to process")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.video_path):
        print(f"Error: Video file not found: {args.video_path}")
        return
    
    try:
        results = process_dashcam_video(
            video_path=args.video_path,
            model_path=args.model,
            output_dir=args.output,
            camera_config=args.camera_config,
            frame_skip=args.frame_skip,
            max_frames=args.max_frames
        )
        
        print(f"\n✓ Successfully processed {len(results)} frames!")
        print(f"Check the output directory: {args.output}")
        
    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
