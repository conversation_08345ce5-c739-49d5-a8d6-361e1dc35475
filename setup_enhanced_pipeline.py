#!/usr/bin/env python3
"""
Enhanced Setup Script for Dashcam to PLY Pipeline
Integrates multiple NVIDIA tools for best results
"""

import os
import sys
import subprocess
import platform
import json
from pathlib import Path

def create_virtual_environment():
    """Create virtual environment for the enhanced pipeline"""
    print("=== Creating Enhanced Virtual Environment ===")
    
    venv_name = "venv_enhanced_3d"
    
    if os.path.exists(venv_name):
        print(f"Virtual environment {venv_name} already exists")
        return venv_name
    
    print(f"Creating virtual environment: {venv_name}")
    subprocess.run([sys.executable, '-m', 'venv', venv_name])
    
    return venv_name

def get_python_executable(venv_path):
    """Get python executable path for the virtual environment"""
    if platform.system() == "Windows":
        return os.path.join(venv_path, "Scripts", "python.exe")
    else:
        return os.path.join(venv_path, "bin", "python")

def install_core_dependencies(venv_path):
    """Install core dependencies"""
    print("\n=== Installing Core Dependencies ===")
    
    python_exe = get_python_executable(venv_path)
    
    # Upgrade pip first
    subprocess.run([python_exe, '-m', 'pip', 'install', '--upgrade', 'pip'])
    
    # Core ML dependencies
    print("Installing PyTorch with CUDA...")
    subprocess.run([
        python_exe, '-m', 'pip', 'install', 
        'torch', 'torchvision', 'torchaudio',
        '--index-url', 'https://download.pytorch.org/whl/cu118'
    ])
    
    # 3D processing libraries
    dependencies = [
        'open3d>=0.17.0',
        'opencv-python',
        'matplotlib',
        'numpy',
        'Pillow',
        'trimesh',
        'plyfile',
        'scikit-image',
        'scipy',
        'tqdm',
        'fire',
        'efficientnet_pytorch==0.7.0',
        'tensorboardX'
    ]
    
    print("Installing 3D processing libraries...")
    for dep in dependencies:
        print(f"  Installing {dep}...")
        subprocess.run([python_exe, '-m', 'pip', 'install', dep])
    
    # Install NKSR
    print("Installing NKSR...")
    subprocess.run([
        python_exe, '-m', 'pip', 'install', 'nksr',
        '-f', 'https://nksr.huangjh.tech/whl/torch-2.0.0+cu118.html'
    ])

def setup_nvidia_repositories():
    """Clone and setup additional NVIDIA repositories"""
    print("\n=== Setting up NVIDIA Repositories ===")
    
    tools_dir = "nvidia_tools"
    os.makedirs(tools_dir, exist_ok=True)
    
    repositories = {
        "SCube": {
            "url": "https://github.com/nv-tlabs/SCube.git",
            "description": "Instant Large-Scale Scene Reconstruction using VoxSplats"
        },
        "3dgrut": {
            "url": "https://github.com/nv-tlabs/3dgrut.git", 
            "description": "Ray tracing and hybrid rasterization of Gaussian particles"
        },
        "FlexiCubes": {
            "url": "https://github.com/nv-tlabs/FlexiCubes.git",
            "description": "Flexible Isosurface Extraction for Gradient-Based Mesh Optimization"
        }
    }
    
    cloned_repos = {}
    
    for name, info in repositories.items():
        repo_dir = os.path.join(tools_dir, name)
        
        if os.path.exists(repo_dir):
            print(f"[OK] {name} already exists")
            cloned_repos[name] = repo_dir
        else:
            print(f"Cloning {name}...")
            try:
                result = subprocess.run([
                    "git", "clone", info["url"], repo_dir
                ], capture_output=True, text=True)

                if result.returncode == 0:
                    print(f"[OK] {name} cloned successfully")
                    cloned_repos[name] = repo_dir
                else:
                    print(f"[WARNING] Failed to clone {name}: {result.stderr}")
            except FileNotFoundError:
                print("[WARNING] Git not found. Please install Git to use additional tools.")
                break
    
    return cloned_repos

def create_configuration_file(venv_path, nvidia_tools):
    """Create configuration file for the pipeline"""
    print("\n=== Creating Configuration ===")
    
    config = {
        "environment": {
            "venv_path": venv_path,
            "python_executable": get_python_executable(venv_path)
        },
        "tools": {
            "lift_splat_shoot": "lift-splat-shoot-master",
            "nvidia_tools": nvidia_tools
        },
        "quality_presets": {
            "fast": {
                "frame_skip": 20,
                "max_frames": 50,
                "nksr_quality": "fast"
            },
            "balanced": {
                "frame_skip": 10,
                "max_frames": 200,
                "nksr_quality": "balanced"
            },
            "high": {
                "frame_skip": 5,
                "max_frames": 400,
                "nksr_quality": "high"
            },
            "ultra": {
                "frame_skip": 3,
                "max_frames": 600,
                "nksr_quality": "ultra"
            }
        }
    }
    
    config_path = "pipeline_config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"[OK] Configuration saved to {config_path}")
    return config_path

def test_installation(venv_path):
    """Test if everything is installed correctly"""
    print("\n=== Testing Installation ===")

    python_exe = get_python_executable(venv_path)

    # Test imports - avoid Unicode characters for Windows compatibility
    test_script = '''
import sys
import torch
import numpy as np
import cv2
import open3d as o3d

print(f"Python: {sys.version}")
print(f"PyTorch: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device: {torch.cuda.get_device_name()}")
print(f"OpenCV: {cv2.__version__}")
print(f"Open3D: {o3d.__version__}")

try:
    import nksr
    print("[OK] NKSR available")
except ImportError:
    print("[ERROR] NKSR not available")

print("All core dependencies working!")
'''
    
    try:
        result = subprocess.run([python_exe, '-c', test_script], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("[OK] Installation test passed!")
            print(result.stdout)
            return True
        else:
            print("[ERROR] Installation test failed!")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"[ERROR] Test failed with exception: {e}")
        return False

def create_usage_examples():
    """Create usage examples"""
    print("\n=== Creating Usage Examples ===")
    
    examples = {
        "basic_usage.py": '''#!/usr/bin/env python3
"""Basic usage example for enhanced dashcam to PLY pipeline"""

from enhanced_dashcam_to_ply import EnhancedDashcamTo3D

# Initialize pipeline
pipeline = EnhancedDashcamTo3D()

# Process your video
result = pipeline.process_video(
    video_path="your_dashcam_video.mp4",
    output_dir="results",
    frame_skip=10,
    max_frames=200,
    quality_level="high"
)

print(f"3D model saved to: {result}")
''',
        
        "batch_processing.py": '''#!/usr/bin/env python3
"""Batch processing example"""

import os
from enhanced_dashcam_to_ply import EnhancedDashcamTo3D

pipeline = EnhancedDashcamTo3D()

# Process all MP4 files in a directory
video_dir = "input_videos"
output_base = "batch_results"

for video_file in os.listdir(video_dir):
    if video_file.endswith('.mp4'):
        video_path = os.path.join(video_dir, video_file)
        output_dir = os.path.join(output_base, video_file.replace('.mp4', ''))
        
        print(f"Processing {video_file}...")
        try:
            result = pipeline.process_video(
                video_path=video_path,
                output_dir=output_dir,
                quality_level="balanced"
            )
            print(f"✓ Success: {result}")
        except Exception as e:
            print(f"❌ Failed: {e}")
''',
        
        "quality_comparison.py": '''#!/usr/bin/env python3
"""Generate models at different quality levels for comparison"""

from enhanced_dashcam_to_ply import EnhancedDashcamTo3D

pipeline = EnhancedDashcamTo3D()
video_path = "test_video.mp4"

quality_levels = ["fast", "balanced", "high", "ultra"]

for quality in quality_levels:
    print(f"\\nProcessing with {quality} quality...")
    
    result = pipeline.process_video(
        video_path=video_path,
        output_dir=f"quality_test_{quality}",
        quality_level=quality
    )
    
    print(f"✓ {quality} quality model: {result}")
'''
    }
    
    examples_dir = "examples"
    os.makedirs(examples_dir, exist_ok=True)
    
    for filename, content in examples.items():
        filepath = os.path.join(examples_dir, filename)
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"✓ Created {filepath}")

def main():
    print("🚀 Enhanced Dashcam to PLY Pipeline Setup")
    print("=" * 50)
    
    # Check prerequisites
    if not os.path.exists("lift-splat-shoot-master"):
        print("❌ Error: lift-splat-shoot-master directory not found!")
        print("Please ensure you have the lift-splat-shoot-master directory")
        return
    
    # Create virtual environment
    venv_path = create_virtual_environment()
    
    # Install dependencies
    install_core_dependencies(venv_path)
    
    # Setup NVIDIA repositories
    nvidia_tools = setup_nvidia_repositories()
    
    # Create configuration
    config_path = create_configuration_file(venv_path, nvidia_tools)
    
    # Test installation
    if not test_installation(venv_path):
        print("❌ Installation test failed. Please check the errors above.")
        return
    
    # Create examples
    create_usage_examples()
    
    print("\n" + "=" * 50)
    print("🎉 Enhanced Pipeline Setup Complete!")
    print("=" * 50)
    
    # Activation instructions
    if platform.system() == "Windows":
        activate_cmd = f"{venv_path}\\Scripts\\activate"
    else:
        activate_cmd = f"source {venv_path}/bin/activate"
    
    print(f"\n📋 Next Steps:")
    print(f"1. Activate the environment:")
    print(f"   {activate_cmd}")
    print(f"\n2. Run the enhanced pipeline:")
    print(f"   python enhanced_dashcam_to_ply.py your_video.mp4")
    print(f"\n3. Or install dependencies automatically:")
    print(f"   python enhanced_dashcam_to_ply.py your_video.mp4 --install-deps")
    
    print(f"\n📁 Files created:")
    print(f"   • enhanced_dashcam_to_ply.py - Main pipeline")
    print(f"   • {config_path} - Configuration")
    print(f"   • examples/ - Usage examples")
    print(f"   • {venv_path}/ - Virtual environment")
    
    if nvidia_tools:
        print(f"   • nvidia_tools/ - Additional NVIDIA tools")
        for tool, path in nvidia_tools.items():
            print(f"     - {tool}: {path}")
    
    # Check for sample video
    sample_video = "lift-splat-shoot-master/DashCam.mp4"
    if os.path.exists(sample_video):
        print(f"\n🎬 Sample video found: {sample_video}")
        print(f"Test with: python enhanced_dashcam_to_ply.py {sample_video}")

if __name__ == "__main__":
    main()
