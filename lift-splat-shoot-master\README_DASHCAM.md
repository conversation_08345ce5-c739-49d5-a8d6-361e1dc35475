# Dashcam 3D Road Reconstruction

This project extends the original Lift-Splat-Shoot model to work with dashcam footage, enabling you to create 3D reconstructions of road areas from single-camera video.

## Overview

The system processes dashcam video files and generates:
- Bird's-eye-view (BEV) occupancy maps
- 3D point clouds of the road area
- Exportable 3D models in PLY and OBJ formats
- Visualizations and analysis

## Quick Start

### 1. Setup Environment

```bash
# Create and activate virtual environment
python -m venv venv
.\venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# Install dependencies
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install fire efficientnet_pytorch==0.7.0 tensorboardX matplotlib opencv-python
```

### 2. Basic Usage

```bash
# Process a dashcam video
python dashcam_inference.py your_dashcam_video.mp4 --output results

# With custom settings
python dashcam_inference.py your_video.mp4 \
    --output my_results \
    --frame-skip 5 \
    --max-frames 200
```

### 3. View Results

The output directory will contain:
- `bev_maps/` - Individual bird's-eye-view maps
- `road_model.ply` - 3D model in PLY format
- `road_model.obj` - 3D model in OBJ format
- `3d_visualization.png` - 3D visualization
- `processing_summary.json` - Processing metadata

## Detailed Usage

### Camera Calibration

For best results, calibrate your camera:

```python
from src.camera_calibration import CameraCalibrator

# Using chessboard calibration images
calibrator = CameraCalibrator()
calibration = calibrator.calibrate_from_chessboard("calibration_images/")
calibrator.save_calibration("my_camera.json", calibration)
```

Then use the calibration:

```bash
python dashcam_inference.py video.mp4 --camera-config my_camera.json
```

### Advanced Processing

```python
from dashcam_inference import process_dashcam_video

# Custom processing
results = process_dashcam_video(
    video_path="dashcam.mp4",
    model_path="pretrained_model.pth",  # Optional pretrained model
    output_dir="output",
    camera_config="camera_config.json",
    frame_skip=10,
    max_frames=100
)
```

### 3D Model Export

```python
from src.visualization_3d import create_road_model_from_results

# Create 3D model from results
vertices, faces = create_road_model_from_results(
    results, 
    grid_conf, 
    output_dir="3d_output",
    threshold=0.3  # Occupancy threshold
)
```

## Configuration

### Grid Configuration

Controls the 3D space being reconstructed:

```python
grid_conf = {
    'xbound': [-25.0, 25.0, 0.5],    # Left-right: min, max, resolution (meters)
    'ybound': [0.0, 50.0, 0.5],      # Forward: min, max, resolution (meters)
    'zbound': [-2.0, 4.0, 6.0],      # Height: min, max, resolution (meters)
    'dbound': [2.0, 50.0, 1.0],      # Depth: min, max, resolution (meters)
}
```

### Camera Parameters

Default parameters work for typical dashcams, but calibration improves accuracy:

```python
camera_params = {
    'intrinsic': [
        [focal_length, 0.0, cx],      # fx, 0, cx
        [0.0, focal_length, cy],      # 0, fy, cy
        [0.0, 0.0, 1.0]              # 0, 0, 1
    ],
    'rotation': [
        [1.0, 0.0, 0.0],             # Camera orientation
        [0.0, 1.0, 0.0],
        [0.0, 0.0, 1.0]
    ],
    'translation': [0.0, 0.0, 1.5]   # Camera position (height above ground)
}
```

## Model Performance

### Without Pretrained Model
- Uses randomly initialized weights
- May produce less accurate results
- Good for testing and development

### With Pretrained Model
- Download from original Lift-Splat-Shoot repository
- Significantly better road segmentation
- Recommended for production use

```bash
# Download pretrained model (example)
wget https://drive.google.com/file/d/1bsUYveW_eOqa4lglryyGQNeC4fyQWvQQ/view
python dashcam_inference.py video.mp4 --model pretrained_model.pth
```

## Tips for Best Results

### Video Quality
- Use high-resolution dashcam footage (1080p or higher)
- Ensure good lighting conditions
- Avoid excessive motion blur
- Stable mounting reduces noise

### Camera Calibration
- Perform proper camera calibration for your specific dashcam
- Use chessboard pattern with at least 10-15 images
- Vary angles and positions during calibration

### Processing Parameters
- Adjust `frame_skip` based on video speed and desired detail
- Lower `threshold` values capture more road area
- Higher `max_frames` provides more complete reconstruction

### Hardware Requirements
- GPU recommended for faster processing
- 8GB+ RAM for large videos
- SSD storage for better I/O performance

## Troubleshooting

### Common Issues

**"Could not open video file"**
- Check video file path and format
- Ensure OpenCV supports the video codec

**"No 3D points generated"**
- Lower the occupancy threshold (try 0.1-0.3)
- Check if the model is producing valid outputs
- Verify camera parameters are reasonable

**"CUDA out of memory"**
- Reduce batch size or use CPU processing
- Process fewer frames at once
- Use smaller input resolution

**Poor reconstruction quality**
- Use a pretrained model
- Improve camera calibration
- Ensure good video quality
- Adjust grid configuration for your scene

### Performance Optimization

```python
# For faster processing
process_dashcam_video(
    video_path="video.mp4",
    frame_skip=20,        # Process fewer frames
    max_frames=50,        # Limit total frames
    # ... other parameters
)
```

## File Structure

```
lift-splat-shoot-master/
├── dashcam_inference.py          # Main inference script
├── src/
│   ├── camera_calibration.py     # Camera calibration utilities
│   ├── visualization_3d.py       # 3D visualization and export
│   ├── data.py                   # Data loading (modified)
│   └── ...                       # Original LSS files
├── venv/                         # Virtual environment
└── README_DASHCAM.md            # This file
```

## Examples

See the `examples/` directory for:
- Sample camera calibration
- Processing different video types
- Custom visualization scripts
- Integration with other tools

## Limitations

- Single camera input (monocular)
- Assumes flat road surface
- Limited to forward-facing camera
- Requires good lighting conditions
- May struggle with complex road geometries

## Future Improvements

- Multi-camera support
- Temporal consistency across frames
- Better handling of dynamic objects
- Integration with SLAM systems
- Real-time processing capabilities

## Citation

If you use this work, please cite the original Lift-Splat-Shoot paper:

```bibtex
@inproceedings{philion2020lift,
    title={Lift, Splat, Shoot: Encoding Images From Arbitrary Camera Rigs by Implicitly Unprojecting to 3D},
    author={Jonah Philion and Sanja Fidler},
    booktitle={Proceedings of the European Conference on Computer Vision},
    year={2020},
}
```
