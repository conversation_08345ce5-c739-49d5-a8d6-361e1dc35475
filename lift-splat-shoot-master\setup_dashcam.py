"""
Setup script for dashcam 3D reconstruction
"""

import os
import sys
import subprocess
import platform


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("Error: Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✓ Python version: {version.major}.{version.minor}.{version.micro}")
    return True


def create_virtual_environment():
    """Create virtual environment"""
    print("Creating virtual environment...")
    
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✓ Virtual environment created")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error creating virtual environment: {e}")
        return False


def get_activation_command():
    """Get the command to activate virtual environment"""
    if platform.system() == "Windows":
        return r".\venv\Scripts\activate"
    else:
        return "source venv/bin/activate"


def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    
    # Determine pip path
    if platform.system() == "Windows":
        pip_path = r".\venv\Scripts\pip"
    else:
        pip_path = "./venv/bin/pip"
    
    try:
        # Install PyTorch with CUDA support
        print("Installing PyTorch with CUDA support...")
        subprocess.run([
            pip_path, "install", "torch", "torchvision", "torchaudio", 
            "--index-url", "https://download.pytorch.org/whl/cu118"
        ], check=True)
        
        # Install other requirements
        print("Installing other dependencies...")
        subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
        
        print("✓ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        print("You may need to install dependencies manually")
        return False


def test_installation():
    """Test if installation is working"""
    print("Testing installation...")
    
    # Determine python path
    if platform.system() == "Windows":
        python_path = r".\venv\Scripts\python"
    else:
        python_path = "./venv/bin/python"
    
    test_script = """
import torch
import cv2
import numpy as np
import matplotlib.pyplot as plt
print("✓ All core dependencies imported successfully")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device: {torch.cuda.get_device_name(0)}")
"""
    
    try:
        result = subprocess.run([python_path, "-c", test_script], 
                              capture_output=True, text=True, check=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Installation test failed: {e}")
        print("Error output:", e.stderr)
        return False


def download_sample_data():
    """Provide information about sample data"""
    print("\n" + "="*50)
    print("SAMPLE DATA")
    print("="*50)
    print("To test the system, you'll need a dashcam video file.")
    print("Place your video file as 'sample_dashcam.mp4' in this directory.")
    print("\nFor best results, use a video with:")
    print("  • Resolution: 1280x720 or higher")
    print("  • Good lighting conditions")
    print("  • Clear road view")
    print("  • Stable camera mounting")


def print_usage_instructions():
    """Print usage instructions"""
    activation_cmd = get_activation_command()
    
    print("\n" + "="*50)
    print("SETUP COMPLETE!")
    print("="*50)
    print("To use the dashcam 3D reconstruction system:")
    print()
    print("1. Activate the virtual environment:")
    print(f"   {activation_cmd}")
    print()
    print("2. Process a dashcam video:")
    print("   python dashcam_inference.py your_video.mp4")
    print()
    print("3. Run the example:")
    print("   python example_usage.py")
    print()
    print("4. For help:")
    print("   python dashcam_inference.py --help")
    print()
    print("See README_DASHCAM.md for detailed documentation.")


def main():
    """Main setup function"""
    print("Dashcam 3D Road Reconstruction - Setup")
    print("="*50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Create virtual environment
    if not os.path.exists("venv"):
        if not create_virtual_environment():
            return False
    else:
        print("✓ Virtual environment already exists")
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Test installation
    if not test_installation():
        print("Warning: Installation test failed, but you can still try to use the system")
    
    # Provide sample data info
    download_sample_data()
    
    # Print usage instructions
    print_usage_instructions()
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        print("\nSetup failed. Please check the error messages above.")
        sys.exit(1)
    else:
        print("\n✓ Setup completed successfully!")
