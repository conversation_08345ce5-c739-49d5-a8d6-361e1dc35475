#!/usr/bin/env python3
"""
Simple test script for Windows compatibility
Tests the enhanced dashcam pipeline without Unicode issues
"""

import os
import sys
import subprocess

def print_status(message, status="INFO"):
    """Print status message with Windows-compatible formatting"""
    status_symbols = {
        "OK": "[OK]",
        "ERROR": "[ERROR]", 
        "WARNING": "[WARNING]",
        "INFO": "[INFO]"
    }
    print(f"{status_symbols.get(status, '[INFO]')} {message}")

def test_basic_imports():
    """Test basic Python imports"""
    print_status("Testing Basic Imports", "INFO")
    
    imports_to_test = [
        ("torch", "PyTorch"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("open3d", "Open3D"),
        ("matplotlib", "Matplotlib"),
        ("PIL", "Pillow")
    ]
    
    failed_imports = []
    
    for module, name in imports_to_test:
        try:
            __import__(module)
            print_status(f"{name} available", "OK")
        except ImportError:
            print_status(f"{name} not available", "ERROR")
            failed_imports.append(name)
    
    # Test NKSR separately
    try:
        import nksr
        print_status("NKSR available", "OK")
    except ImportError:
        print_status("NKSR not available", "ERROR")
        failed_imports.append("NKSR")
    
    return len(failed_imports) == 0, failed_imports

def test_cuda():
    """Test CUDA availability"""
    print_status("Testing CUDA", "INFO")
    
    try:
        import torch
        if torch.cuda.is_available():
            print_status("CUDA available", "OK")
            print_status(f"Device count: {torch.cuda.device_count()}", "INFO")
            print_status(f"Current device: {torch.cuda.current_device()}", "INFO")
            print_status(f"Device name: {torch.cuda.get_device_name()}", "INFO")
            return True
        else:
            print_status("CUDA not available (will use CPU)", "WARNING")
            return False
    except ImportError:
        print_status("PyTorch not available", "ERROR")
        return False

def test_file_structure():
    """Test required file structure"""
    print_status("Testing File Structure", "INFO")
    
    required_files = [
        "lift-splat-shoot-master",
        "lift-splat-shoot-master/dashcam_inference.py",
        "enhanced_dashcam_to_ply.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print_status(f"{file_path} found", "OK")
        else:
            print_status(f"{file_path} not found", "ERROR")
            missing_files.append(file_path)
    
    # Check for sample video
    sample_videos = [
        "lift-splat-shoot-master/DashCam.mp4",
        "sample_video.mp4",
        "test_video.mp4"
    ]
    
    found_video = None
    for video in sample_videos:
        if os.path.exists(video):
            found_video = video
            print_status(f"Sample video found: {video}", "OK")
            break
    
    if not found_video:
        print_status("No sample video found for testing", "WARNING")
    
    return len(missing_files) == 0, missing_files, found_video

def run_quick_pipeline_test(sample_video):
    """Run a very quick test of the pipeline"""
    print_status("Running Quick Pipeline Test", "INFO")
    
    if not sample_video:
        print_status("No sample video available for testing", "WARNING")
        return False
    
    # Very minimal test
    cmd = [
        sys.executable, "enhanced_dashcam_to_ply.py",
        sample_video,
        "--output-dir", "simple_test_output",
        "--frame-skip", "50",  # Very sparse
        "--max-frames", "5",   # Very few frames
        "--quality", "fast"
    ]
    
    print_status(f"Running: {' '.join(cmd)}", "INFO")
    print_status("This may take a few minutes...", "INFO")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              timeout=300, encoding='utf-8', errors='replace')
        
        if result.returncode == 0:
            print_status("Quick test completed successfully!", "OK")
            
            # Check output
            output_file = "simple_test_output/high_quality_3d_model_fast.ply"
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)
                print_status(f"Output PLY created: {output_file} ({file_size:.1f} MB)", "OK")
                return True
            else:
                print_status("Test completed but no output file found", "WARNING")
                return False
        else:
            print_status("Quick test failed", "ERROR")
            print("STDOUT:", result.stdout[-500:])  # Last 500 chars
            print("STDERR:", result.stderr[-500:])  # Last 500 chars
            return False
            
    except subprocess.TimeoutExpired:
        print_status("Test timed out (took longer than 5 minutes)", "ERROR")
        return False
    except Exception as e:
        print_status(f"Test failed with exception: {e}", "ERROR")
        return False

def main():
    print("Simple Test for Enhanced Dashcam to PLY Pipeline")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test 1: Basic imports
    imports_ok, failed_imports = test_basic_imports()
    if not imports_ok:
        print_status(f"Missing dependencies: {', '.join(failed_imports)}", "ERROR")
        print_status("Run: python windows_compatible_setup.py", "INFO")
        all_tests_passed = False
    
    print()
    
    # Test 2: CUDA
    cuda_ok = test_cuda()
    
    print()
    
    # Test 3: File structure
    files_ok, missing_files, sample_video = test_file_structure()
    if not files_ok:
        print_status(f"Missing files: {', '.join(missing_files)}", "ERROR")
        all_tests_passed = False
    
    print()
    
    # Summary
    if all_tests_passed:
        print_status("All basic tests passed!", "OK")
        
        if sample_video:
            response = input("Run quick pipeline test? (y/n): ").lower().strip()
            if response in ['y', 'yes']:
                pipeline_ok = run_quick_pipeline_test(sample_video)
                if pipeline_ok:
                    print_status("Pipeline test successful!", "OK")
                    print_status("You can now process your own videos:", "INFO")
                    print_status("python enhanced_dashcam_to_ply.py your_video.mp4", "INFO")
                else:
                    print_status("Pipeline test failed", "ERROR")
        else:
            print_status("Ready to process your videos:", "INFO")
            print_status("python enhanced_dashcam_to_ply.py your_video.mp4", "INFO")
    else:
        print_status("Some tests failed. Please fix the issues above.", "ERROR")
    
    print()
    print_status("Performance recommendations:", "INFO")
    if cuda_ok:
        print_status("CUDA available - use --quality high for best results", "INFO")
    else:
        print_status("CPU-only mode - use --quality balanced for reasonable speed", "INFO")

if __name__ == "__main__":
    main()
