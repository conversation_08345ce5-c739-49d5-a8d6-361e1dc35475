"""
3D Visualization and Export utilities for BEV maps
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import os
from typing import List, Dict, Tuple
import json


def bev_to_3d_points(bev_map: np.ndarray, grid_conf: Dict, threshold: float = 0.5) -> np.ndarray:
    """
    Convert BEV occupancy map to 3D point cloud
    
    Args:
        bev_map: 2D occupancy map from model output
        grid_conf: Grid configuration with bounds
        threshold: Occupancy threshold for point generation
        
    Returns:
        Array of 3D points (N, 3) where each row is [x, y, z]
    """
    # Get grid bounds
    x_min, x_max, x_res = grid_conf['xbound']
    y_min, y_max, y_res = grid_conf['ybound']
    z_min, z_max, z_res = grid_conf['zbound']
    
    # Find occupied cells
    occupied = bev_map > threshold
    y_indices, x_indices = np.where(occupied)
    
    # Convert indices to real-world coordinates
    x_coords = x_min + (x_indices + 0.5) * x_res
    y_coords = y_min + (y_indices + 0.5) * y_res
    
    # For road surface, use ground level (z=0) with some variation based on occupancy
    z_coords = np.zeros_like(x_coords)
    
    # Add height variation based on occupancy probability
    occupancy_values = bev_map[y_indices, x_indices]
    z_coords += (occupancy_values - 0.5) * 0.2  # Small height variation
    
    # Stack coordinates
    points_3d = np.column_stack([x_coords, y_coords, z_coords])
    
    return points_3d


def create_road_mesh(bev_maps: List[np.ndarray], grid_conf: Dict, 
                    threshold: float = 0.5) -> Tuple[np.ndarray, np.ndarray]:
    """
    Create a 3D mesh from multiple BEV maps (temporal sequence)
    
    Args:
        bev_maps: List of BEV occupancy maps
        grid_conf: Grid configuration
        threshold: Occupancy threshold
        
    Returns:
        Tuple of (vertices, faces) for 3D mesh
    """
    all_points = []
    
    # Process each BEV map
    for i, bev_map in enumerate(bev_maps):
        points = bev_to_3d_points(bev_map, grid_conf, threshold)
        
        # Add temporal dimension (y-offset for sequence visualization)
        if len(bev_maps) > 1:
            time_offset = i * 2.0  # 2 meter spacing between time steps
            points[:, 1] += time_offset
        
        all_points.append(points)
    
    # Combine all points
    if all_points:
        vertices = np.vstack(all_points)
    else:
        vertices = np.empty((0, 3))
    
    # For simplicity, we'll create a point cloud rather than a complex mesh
    # In a full implementation, you might use Delaunay triangulation or marching cubes
    faces = np.empty((0, 3), dtype=int)  # Empty faces array
    
    return vertices, faces


def export_to_ply(vertices: np.ndarray, faces: np.ndarray, filepath: str, 
                 colors: np.ndarray = None):
    """
    Export 3D data to PLY format
    
    Args:
        vertices: Array of 3D vertices (N, 3)
        faces: Array of face indices (M, 3)
        filepath: Output file path
        colors: Optional vertex colors (N, 3) in range [0, 255]
    """
    with open(filepath, 'w') as f:
        # Write PLY header
        f.write("ply\n")
        f.write("format ascii 1.0\n")
        f.write(f"element vertex {len(vertices)}\n")
        f.write("property float x\n")
        f.write("property float y\n")
        f.write("property float z\n")
        
        if colors is not None:
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
        
        if len(faces) > 0:
            f.write(f"element face {len(faces)}\n")
            f.write("property list uchar int vertex_indices\n")
        
        f.write("end_header\n")
        
        # Write vertices
        for i, vertex in enumerate(vertices):
            if colors is not None:
                color = colors[i].astype(int)
                f.write(f"{vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f} {color[0]} {color[1]} {color[2]}\n")
            else:
                f.write(f"{vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")
        
        # Write faces
        for face in faces:
            f.write(f"3 {face[0]} {face[1]} {face[2]}\n")
    
    print(f"✓ Exported PLY file: {filepath}")


def export_to_obj(vertices: np.ndarray, faces: np.ndarray, filepath: str):
    """
    Export 3D data to OBJ format
    
    Args:
        vertices: Array of 3D vertices (N, 3)
        faces: Array of face indices (M, 3)
        filepath: Output file path
    """
    with open(filepath, 'w') as f:
        f.write("# 3D Road Model from Dashcam\n")
        f.write("# Generated by Lift-Splat-Shoot\n\n")
        
        # Write vertices
        for vertex in vertices:
            f.write(f"v {vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")
        
        # Write faces (OBJ uses 1-based indexing)
        for face in faces:
            f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
    
    print(f"✓ Exported OBJ file: {filepath}")


def visualize_3d_road(vertices: np.ndarray, title: str = "3D Road Reconstruction", 
                     save_path: str = None):
    """
    Create 3D visualization of road points
    
    Args:
        vertices: Array of 3D vertices (N, 3)
        title: Plot title
        save_path: Optional path to save the plot
    """
    if len(vertices) == 0:
        print("No vertices to visualize")
        return
    
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # Create scatter plot
    scatter = ax.scatter(vertices[:, 0], vertices[:, 1], vertices[:, 2], 
                        c=vertices[:, 2], cmap='viridis', s=1, alpha=0.6)
    
    ax.set_xlabel('X (meters)')
    ax.set_ylabel('Y (meters)')
    ax.set_zlabel('Z (meters)')
    ax.set_title(title)
    
    # Add colorbar
    plt.colorbar(scatter, ax=ax, label='Height (meters)')
    
    # Set equal aspect ratio
    max_range = np.array([vertices[:, 0].max() - vertices[:, 0].min(),
                         vertices[:, 1].max() - vertices[:, 1].min(),
                         vertices[:, 2].max() - vertices[:, 2].min()]).max() / 2.0
    
    mid_x = (vertices[:, 0].max() + vertices[:, 0].min()) * 0.5
    mid_y = (vertices[:, 1].max() + vertices[:, 1].min()) * 0.5
    mid_z = (vertices[:, 2].max() + vertices[:, 2].min()) * 0.5
    
    ax.set_xlim(mid_x - max_range, mid_x + max_range)
    ax.set_ylim(mid_y - max_range, mid_y + max_range)
    ax.set_zlim(mid_z - max_range, mid_z + max_range)
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✓ 3D visualization saved: {save_path}")
    
    plt.show()


def create_road_model_from_results(results: List[Dict], grid_conf: Dict,
                                  output_dir: str, threshold: float = 0.5):
    """
    Create 3D road model from processing results with multiple approaches

    Args:
        results: List of processing results with BEV maps
        grid_conf: Grid configuration
        output_dir: Output directory for 3D files
        threshold: Occupancy threshold for 3D reconstruction
    """
    print("Creating 3D road model...")

    # Extract BEV maps
    bev_maps = [result['bev_output'] for result in results]

    # Try multiple thresholds to ensure we get 3D points
    thresholds_to_try = [threshold, threshold * 0.5, threshold * 0.1, 0.01, 0.001]
    vertices = None
    faces = None
    used_threshold = threshold

    for thresh in thresholds_to_try:
        print(f"Trying threshold: {thresh}")
        vertices, faces = create_road_mesh(bev_maps, grid_conf, thresh)
        if len(vertices) > 0:
            used_threshold = thresh
            print(f"✓ Generated {len(vertices)} 3D points with threshold {thresh}")
            break
        else:
            print(f"  No points with threshold {thresh}")

    if vertices is None or len(vertices) == 0:
        print("Warning: No 3D points generated even with very low thresholds.")
        print("This might indicate the model needs better training or the BEV maps are too sparse.")

        # Create a simple grid as fallback
        vertices = create_fallback_road_surface(grid_conf)
        faces = np.empty((0, 3), dtype=int)
        used_threshold = 0.0
        print(f"Created fallback road surface with {len(vertices)} points")

    print(f"Final model: {len(vertices)} vertices, {len(faces)} faces")

    # Create multiple 3D model variants
    create_multiple_3d_formats(vertices, faces, grid_conf, output_dir, used_threshold, bev_maps)

    return vertices, faces


def create_fallback_road_surface(grid_conf: Dict) -> np.ndarray:
    """Create a simple road surface grid as fallback"""
    x_min, x_max, x_res = grid_conf['xbound']
    y_min, y_max, y_res = grid_conf['ybound']

    # Create a grid of points representing road surface
    x_points = np.arange(x_min, x_max, x_res * 2)  # Sparser grid
    y_points = np.arange(y_min, y_max, y_res * 2)

    vertices = []
    for x in x_points:
        for y in y_points:
            # Create road surface at z=0 with slight variations
            z = np.random.normal(0, 0.1)  # Small height variation
            vertices.append([x, y, z])

    return np.array(vertices)


def create_multiple_3d_formats(vertices: np.ndarray, faces: np.ndarray, grid_conf: Dict,
                              output_dir: str, threshold: float, bev_maps: List[np.ndarray]):
    """Create multiple 3D model formats and visualizations"""

    # Create colors based on different schemes
    colors_height = create_height_colors(vertices)
    colors_intensity = create_intensity_colors(vertices, bev_maps, grid_conf)

    # Export in multiple formats
    formats = [
        ("road_model_height_colored.ply", colors_height),
        ("road_model_intensity_colored.ply", colors_intensity),
        ("road_model_simple.ply", None)
    ]

    for filename, colors in formats:
        ply_path = os.path.join(output_dir, filename)
        export_to_ply(vertices, faces, ply_path, colors)
        print(f"✓ Exported: {filename}")

    # Export OBJ files
    obj_path = os.path.join(output_dir, "road_model.obj")
    export_to_obj(vertices, faces, obj_path)

    # Create enhanced visualizations
    create_enhanced_visualizations(vertices, output_dir, grid_conf)

    # Create dense point cloud
    dense_vertices = create_dense_point_cloud(bev_maps, grid_conf, threshold)
    if len(dense_vertices) > 0:
        dense_ply_path = os.path.join(output_dir, "road_model_dense.ply")
        dense_colors = create_height_colors(dense_vertices)
        export_to_ply(dense_vertices, np.empty((0, 3), dtype=int), dense_ply_path, dense_colors)
        print(f"✓ Exported dense point cloud: {len(dense_vertices)} points")

    # Save comprehensive metadata
    save_comprehensive_metadata(vertices, faces, grid_conf, threshold, output_dir, bev_maps)


def create_height_colors(vertices: np.ndarray) -> np.ndarray:
    """Create colors based on height (z-coordinate)"""
    if len(vertices) == 0:
        return np.empty((0, 3))

    z_min, z_max = vertices[:, 2].min(), vertices[:, 2].max()
    if z_max > z_min:
        normalized_z = (vertices[:, 2] - z_min) / (z_max - z_min)
    else:
        normalized_z = np.zeros(len(vertices))

    colors = np.zeros((len(vertices), 3))
    colors[:, 0] = normalized_z * 255  # Red channel
    colors[:, 2] = (1 - normalized_z) * 255  # Blue channel
    colors[:, 1] = 128  # Green channel

    return colors.astype(np.uint8)


def create_intensity_colors(vertices: np.ndarray, bev_maps: List[np.ndarray],
                          grid_conf: Dict) -> np.ndarray:
    """Create colors based on BEV intensity values"""
    if len(vertices) == 0 or len(bev_maps) == 0:
        return np.empty((0, 3))

    # Use the first BEV map for intensity
    bev_map = bev_maps[0]
    x_min, x_max, x_res = grid_conf['xbound']
    y_min, y_max, y_res = grid_conf['ybound']

    colors = np.zeros((len(vertices), 3))

    for i, vertex in enumerate(vertices):
        x, y, z = vertex
        # Convert world coordinates to BEV map indices
        x_idx = int((x - x_min) / x_res)
        y_idx = int((y - y_min) / y_res)

        # Clamp indices
        x_idx = max(0, min(x_idx, bev_map.shape[1] - 1))
        y_idx = max(0, min(y_idx, bev_map.shape[0] - 1))

        intensity = bev_map[y_idx, x_idx]

        # Create color based on intensity
        colors[i, 0] = intensity * 255  # Red
        colors[i, 1] = intensity * 255  # Green
        colors[i, 2] = 255 - intensity * 255  # Blue (inverted)

    return colors.astype(np.uint8)


def create_dense_point_cloud(bev_maps: List[np.ndarray], grid_conf: Dict,
                            threshold: float) -> np.ndarray:
    """Create a dense point cloud from all BEV maps"""
    all_points = []

    for i, bev_map in enumerate(bev_maps):
        points = bev_to_3d_points(bev_map, grid_conf, threshold * 0.1)  # Lower threshold
        if len(points) > 0:
            # Add temporal offset for sequence
            time_offset = i * 1.0  # 1 meter spacing
            points[:, 1] += time_offset
            all_points.append(points)

    if all_points:
        return np.vstack(all_points)
    else:
        return np.empty((0, 3))


def create_enhanced_visualizations(vertices: np.ndarray, output_dir: str, grid_conf: Dict):
    """Create multiple visualization views"""
    if len(vertices) == 0:
        return

    # Top-down view
    plt.figure(figsize=(12, 10))
    plt.scatter(vertices[:, 0], vertices[:, 1], c=vertices[:, 2], cmap='viridis', s=1)
    plt.colorbar(label='Height (m)')
    plt.xlabel('X (meters)')
    plt.ylabel('Y (meters)')
    plt.title('Road Model - Top View')
    plt.axis('equal')
    plt.savefig(os.path.join(output_dir, "road_model_top_view.png"), dpi=150, bbox_inches='tight')
    plt.close()

    # Side view
    plt.figure(figsize=(12, 6))
    plt.scatter(vertices[:, 1], vertices[:, 2], c=vertices[:, 0], cmap='plasma', s=1)
    plt.colorbar(label='X Position (m)')
    plt.xlabel('Y (meters)')
    plt.ylabel('Z (meters)')
    plt.title('Road Model - Side View')
    plt.savefig(os.path.join(output_dir, "road_model_side_view.png"), dpi=150, bbox_inches='tight')
    plt.close()

    # 3D visualization
    visualize_3d_road(vertices, "3D Road Reconstruction from Dashcam",
                     os.path.join(output_dir, "3d_visualization.png"))


def save_comprehensive_metadata(vertices: np.ndarray, faces: np.ndarray, grid_conf: Dict,
                              threshold: float, output_dir: str, bev_maps: List[np.ndarray]):
    """Save comprehensive metadata about the 3D model"""
    metadata = {
        'model_info': {
            'num_vertices': len(vertices),
            'num_faces': len(faces),
            'threshold_used': threshold,
            'num_bev_maps': len(bev_maps)
        },
        'spatial_bounds': {
            'x_range': [float(vertices[:, 0].min()), float(vertices[:, 0].max())] if len(vertices) > 0 else [0, 0],
            'y_range': [float(vertices[:, 1].min()), float(vertices[:, 1].max())] if len(vertices) > 0 else [0, 0],
            'z_range': [float(vertices[:, 2].min()), float(vertices[:, 2].max())] if len(vertices) > 0 else [0, 0]
        },
        'grid_config': grid_conf,
        'files_created': [
            'road_model_height_colored.ply',
            'road_model_intensity_colored.ply',
            'road_model_simple.ply',
            'road_model.obj',
            'road_model_dense.ply',
            '3d_visualization.png',
            'road_model_top_view.png',
            'road_model_side_view.png'
        ],
        'usage_instructions': {
            'ply_files': 'Open with MeshLab, CloudCompare, or Blender',
            'obj_files': 'Open with any 3D modeling software',
            'png_files': 'View directly or use for documentation'
        }
    }

    metadata_path = os.path.join(output_dir, "3d_model_metadata.json")
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)

    print(f"✓ Comprehensive 3D model package created!")
    print(f"  - Multiple PLY formats with different coloring")
    print(f"  - OBJ format for universal compatibility")
    print(f"  - Dense point cloud version")
    print(f"  - Multiple visualization views")
    print(f"  - Comprehensive metadata: {metadata_path}")
