"""
Copyright (C) 2020 NVIDIA Corporation.  All rights reserved.
Licensed under the NVIDIA Source Code License. See LICENSE at https://github.com/nv-tlabs/lift-splat-shoot.
Authors: <AUTHORS>
"""

import torch
import os
import numpy as np
from PIL import Image
import cv2
from glob import glob

# Try to import tools - some functions may not be available without nuScenes
try:
    from .tools import img_transform, normalize_img, gen_dx_bx, get_lidar_data
    TOOLS_AVAILABLE = True
except ImportError:
    TOOLS_AVAILABLE = False

# Optional imports for nuScenes functionality
try:
    from pyquaternion import Quaternion
    from nuscenes.nuscenes import NuScenes
    from nuscenes.utils.splits import create_splits_scenes
    from nuscenes.utils.data_classes import Box
    NUSCENES_AVAILABLE = True
except ImportError:
    NUSCENES_AVAILABLE = False
    print("Warning: nuScenes dependencies not available. Only dashcam functionality will work.")

# If tools not available, define essential functions locally
if not TOOLS_AVAILABLE:
    def normalize_img(img):
        """Normalize image to [-1, 1]"""
        return torch.tensor(np.array(img)).float().permute(2, 0, 1) / 255.0 * 2.0 - 1.0

    def img_transform(img, post_rot, post_tran, resize, resize_dims, crop, flip, rotate):
        """Basic image transformation"""
        # Resize
        img = img.resize(resize_dims)

        # Crop
        img = img.crop(crop)

        # Flip
        if flip:
            img = img.transpose(Image.FLIP_LEFT_RIGHT)

        # For simplicity, we'll skip rotation in the basic version
        # Return identity transformations
        post_rot2 = torch.eye(2)
        post_tran2 = torch.zeros(2)

        return img, post_rot2, post_tran2

    def gen_dx_bx(xbound, ybound, zbound):
        """Generate grid parameters"""
        dx = torch.Tensor([xbound[2], ybound[2], zbound[2]])
        bx = torch.Tensor([xbound[0] + xbound[2]/2.0, ybound[0] + ybound[2]/2.0, zbound[0] + zbound[2]/2.0])
        nx = torch.LongTensor([(xbound[1] - xbound[0]) / xbound[2],
                              (ybound[1] - ybound[0]) / ybound[2],
                              (zbound[1] - zbound[0]) / zbound[2]])
        return dx, bx, nx


class NuscData(torch.utils.data.Dataset):
    def __init__(self, nusc, is_train, data_aug_conf, grid_conf):
        self.nusc = nusc
        self.is_train = is_train
        self.data_aug_conf = data_aug_conf
        self.grid_conf = grid_conf

        self.scenes = self.get_scenes()
        self.ixes = self.prepro()

        dx, bx, nx = gen_dx_bx(grid_conf['xbound'], grid_conf['ybound'], grid_conf['zbound'])
        self.dx, self.bx, self.nx = dx.numpy(), bx.numpy(), nx.numpy()

        self.fix_nuscenes_formatting()

        print(self)

    def fix_nuscenes_formatting(self):
        """If nuscenes is stored with trainval/1 trainval/2 ... structure, adjust the file paths
        stored in the nuScenes object.
        """
        # check if default file paths work
        rec = self.ixes[0]
        sampimg = self.nusc.get('sample_data', rec['data']['CAM_FRONT'])
        imgname = os.path.join(self.nusc.dataroot, sampimg['filename'])

        def find_name(f):
            d, fi = os.path.split(f)
            d, di = os.path.split(d)
            d, d0 = os.path.split(d)
            d, d1 = os.path.split(d)
            d, d2 = os.path.split(d)
            return di, fi, f'{d2}/{d1}/{d0}/{di}/{fi}'

        # adjust the image paths if needed
        if not os.path.isfile(imgname):
            print('adjusting nuscenes file paths')
            fs = glob(os.path.join(self.nusc.dataroot, 'samples/*/samples/CAM*/*.jpg'))
            fs += glob(os.path.join(self.nusc.dataroot, 'samples/*/samples/LIDAR_TOP/*.pcd.bin'))
            info = {}
            for f in fs:
                di, fi, fname = find_name(f)
                info[f'samples/{di}/{fi}'] = fname
            fs = glob(os.path.join(self.nusc.dataroot, 'sweeps/*/sweeps/LIDAR_TOP/*.pcd.bin'))
            for f in fs:
                di, fi, fname = find_name(f)
                info[f'sweeps/{di}/{fi}'] = fname
            for rec in self.nusc.sample_data:
                if rec['channel'] == 'LIDAR_TOP' or (rec['is_key_frame'] and rec['channel'] in self.data_aug_conf['cams']):
                    rec['filename'] = info[rec['filename']]

    
    def get_scenes(self):
        # filter by scene split
        split = {
            'v1.0-trainval': {True: 'train', False: 'val'},
            'v1.0-mini': {True: 'mini_train', False: 'mini_val'},
        }[self.nusc.version][self.is_train]

        scenes = create_splits_scenes()[split]

        return scenes

    def prepro(self):
        samples = [samp for samp in self.nusc.sample]

        # remove samples that aren't in this split
        samples = [samp for samp in samples if
                   self.nusc.get('scene', samp['scene_token'])['name'] in self.scenes]

        # sort by scene, timestamp (only to make chronological viz easier)
        samples.sort(key=lambda x: (x['scene_token'], x['timestamp']))

        return samples
    
    def sample_augmentation(self):
        H, W = self.data_aug_conf['H'], self.data_aug_conf['W']
        fH, fW = self.data_aug_conf['final_dim']
        if self.is_train:
            resize = np.random.uniform(*self.data_aug_conf['resize_lim'])
            resize_dims = (int(W*resize), int(H*resize))
            newW, newH = resize_dims
            crop_h = int((1 - np.random.uniform(*self.data_aug_conf['bot_pct_lim']))*newH) - fH
            crop_w = int(np.random.uniform(0, max(0, newW - fW)))
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            if self.data_aug_conf['rand_flip'] and np.random.choice([0, 1]):
                flip = True
            rotate = np.random.uniform(*self.data_aug_conf['rot_lim'])
        else:
            resize = max(fH/H, fW/W)
            resize_dims = (int(W*resize), int(H*resize))
            newW, newH = resize_dims
            crop_h = int((1 - np.mean(self.data_aug_conf['bot_pct_lim']))*newH) - fH
            crop_w = int(max(0, newW - fW) / 2)
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            rotate = 0
        return resize, resize_dims, crop, flip, rotate

    def get_image_data(self, rec, cams):
        imgs = []
        rots = []
        trans = []
        intrins = []
        post_rots = []
        post_trans = []
        for cam in cams:
            samp = self.nusc.get('sample_data', rec['data'][cam])
            imgname = os.path.join(self.nusc.dataroot, samp['filename'])
            img = Image.open(imgname)
            post_rot = torch.eye(2)
            post_tran = torch.zeros(2)

            sens = self.nusc.get('calibrated_sensor', samp['calibrated_sensor_token'])
            intrin = torch.Tensor(sens['camera_intrinsic'])
            rot = torch.Tensor(Quaternion(sens['rotation']).rotation_matrix)
            tran = torch.Tensor(sens['translation'])

            # augmentation (resize, crop, horizontal flip, rotate)
            resize, resize_dims, crop, flip, rotate = self.sample_augmentation()
            img, post_rot2, post_tran2 = img_transform(img, post_rot, post_tran,
                                                     resize=resize,
                                                     resize_dims=resize_dims,
                                                     crop=crop,
                                                     flip=flip,
                                                     rotate=rotate,
                                                     )
            
            # for convenience, make augmentation matrices 3x3
            post_tran = torch.zeros(3)
            post_rot = torch.eye(3)
            post_tran[:2] = post_tran2
            post_rot[:2, :2] = post_rot2

            imgs.append(normalize_img(img))
            intrins.append(intrin)
            rots.append(rot)
            trans.append(tran)
            post_rots.append(post_rot)
            post_trans.append(post_tran)

        return (torch.stack(imgs), torch.stack(rots), torch.stack(trans),
                torch.stack(intrins), torch.stack(post_rots), torch.stack(post_trans))

    def get_lidar_data(self, rec, nsweeps):
        pts = get_lidar_data(self.nusc, rec,
                       nsweeps=nsweeps, min_distance=2.2)
        return torch.Tensor(pts)[:3]  # x,y,z

    def get_binimg(self, rec):
        egopose = self.nusc.get('ego_pose',
                                self.nusc.get('sample_data', rec['data']['LIDAR_TOP'])['ego_pose_token'])
        trans = -np.array(egopose['translation'])
        rot = Quaternion(egopose['rotation']).inverse
        img = np.zeros((self.nx[0], self.nx[1]))
        for tok in rec['anns']:
            inst = self.nusc.get('sample_annotation', tok)
            # add category for lyft
            if not inst['category_name'].split('.')[0] == 'vehicle':
                continue
            box = Box(inst['translation'], inst['size'], Quaternion(inst['rotation']))
            box.translate(trans)
            box.rotate(rot)

            pts = box.bottom_corners()[:2].T
            pts = np.round(
                (pts - self.bx[:2] + self.dx[:2]/2.) / self.dx[:2]
                ).astype(np.int32)
            pts[:, [1, 0]] = pts[:, [0, 1]]
            cv2.fillPoly(img, [pts], 1.0)

        return torch.Tensor(img).unsqueeze(0)

    def choose_cams(self):
        if self.is_train and self.data_aug_conf['Ncams'] < len(self.data_aug_conf['cams']):
            cams = np.random.choice(self.data_aug_conf['cams'], self.data_aug_conf['Ncams'],
                                    replace=False)
        else:
            cams = self.data_aug_conf['cams']
        return cams

    def __str__(self):
        return f"""NuscData: {len(self)} samples. Split: {"train" if self.is_train else "val"}.
                   Augmentation Conf: {self.data_aug_conf}"""

    def __len__(self):
        return len(self.ixes)


class VizData(NuscData):
    def __init__(self, *args, **kwargs):
        super(VizData, self).__init__(*args, **kwargs)
    
    def __getitem__(self, index):
        rec = self.ixes[index]
        
        cams = self.choose_cams()
        imgs, rots, trans, intrins, post_rots, post_trans = self.get_image_data(rec, cams)
        lidar_data = self.get_lidar_data(rec, nsweeps=3)
        binimg = self.get_binimg(rec)
        
        return imgs, rots, trans, intrins, post_rots, post_trans, lidar_data, binimg


class SegmentationData(NuscData):
    def __init__(self, *args, **kwargs):
        super(SegmentationData, self).__init__(*args, **kwargs)
    
    def __getitem__(self, index):
        rec = self.ixes[index]

        cams = self.choose_cams()
        imgs, rots, trans, intrins, post_rots, post_trans = self.get_image_data(rec, cams)
        binimg = self.get_binimg(rec)
        
        return imgs, rots, trans, intrins, post_rots, post_trans, binimg


def worker_rnd_init(x):
    np.random.seed(13 + x)


class DashcamData(torch.utils.data.Dataset):
    def __init__(self, video_path, data_aug_conf, grid_conf, camera_params=None, frame_skip=1):
        """
        Custom dataset for dashcam footage

        Args:
            video_path: Path to dashcam video file
            data_aug_conf: Data augmentation configuration
            grid_conf: Grid configuration for BEV
            camera_params: Dictionary with camera intrinsics and extrinsics
            frame_skip: Skip frames to reduce dataset size
        """
        self.video_path = video_path
        self.data_aug_conf = data_aug_conf
        self.grid_conf = grid_conf
        self.frame_skip = frame_skip

        # Load video and get frame count
        self.cap = cv2.VideoCapture(video_path)
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)

        # Generate frame indices with skipping
        self.frame_indices = list(range(0, self.total_frames, frame_skip))

        # Set default camera parameters if not provided
        if camera_params is None:
            # Default parameters for typical dashcam (these should be calibrated for your specific camera)
            self.camera_params = {
                'intrinsic': [[800.0, 0.0, 640.0],    # fx, 0, cx
                             [0.0, 800.0, 360.0],      # 0, fy, cy
                             [0.0, 0.0, 1.0]],         # 0, 0, 1
                'rotation': [[1.0, 0.0, 0.0],         # Identity rotation (camera facing forward)
                            [0.0, 1.0, 0.0],
                            [0.0, 0.0, 1.0]],
                'translation': [0.0, 0.0, 1.5]        # Camera height above ground (meters)
            }
        else:
            self.camera_params = camera_params

        dx, bx, nx = gen_dx_bx(grid_conf['xbound'], grid_conf['ybound'], grid_conf['zbound'])
        self.dx, self.bx, self.nx = dx.numpy(), bx.numpy(), nx.numpy()

        print(f"Loaded dashcam dataset: {len(self.frame_indices)} frames from {video_path}")

    def __len__(self):
        return len(self.frame_indices)

    def __getitem__(self, index):
        frame_idx = self.frame_indices[index]

        # Read frame from video
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = self.cap.read()

        if not ret:
            raise ValueError(f"Could not read frame {frame_idx}")

        # Convert BGR to RGB
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        img = Image.fromarray(frame)

        # Get camera parameters
        intrin = torch.Tensor(self.camera_params['intrinsic'])
        rot = torch.Tensor(self.camera_params['rotation'])
        tran = torch.Tensor(self.camera_params['translation'])

        # Apply data augmentation
        post_rot = torch.eye(2)
        post_tran = torch.zeros(2)

        resize, resize_dims, crop, flip, rotate = self.sample_augmentation()
        img, post_rot2, post_tran2 = img_transform(img, post_rot, post_tran,
                                                 resize=resize,
                                                 resize_dims=resize_dims,
                                                 crop=crop,
                                                 flip=flip,
                                                 rotate=rotate)

        # Make augmentation matrices 3x3
        post_tran_3d = torch.zeros(3)
        post_rot_3d = torch.eye(3)
        post_tran_3d[:2] = post_tran2
        post_rot_3d[:2, :2] = post_rot2

        # Normalize image
        img_normalized = normalize_img(img)

        # For single camera, we need to add batch dimension
        imgs = img_normalized.unsqueeze(0)  # Add camera dimension
        rots = rot.unsqueeze(0)
        trans = tran.unsqueeze(0)
        intrins = intrin.unsqueeze(0)
        post_rots = post_rot_3d.unsqueeze(0)
        post_trans = post_tran_3d.unsqueeze(0)

        return imgs, rots, trans, intrins, post_rots, post_trans

    def sample_augmentation(self):
        """Sample data augmentation parameters"""
        H, W = self.data_aug_conf['H'], self.data_aug_conf['W']
        fH, fW = self.data_aug_conf['final_dim']

        # For inference, use deterministic augmentation
        resize = max(fH/H, fW/W)
        resize_dims = (int(W*resize), int(H*resize))
        newW, newH = resize_dims
        crop_h = int((1 - np.mean([0.0, 0.22]))*newH) - fH  # Use default bot_pct_lim
        crop_w = int(max(0, newW - fW) / 2)
        crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
        flip = False
        rotate = 0
        return resize, resize_dims, crop, flip, rotate

    def __del__(self):
        if hasattr(self, 'cap'):
            self.cap.release()


def compile_data(version, dataroot, data_aug_conf, grid_conf, bsz,
                 nworkers, parser_name):
    if not NUSCENES_AVAILABLE:
        raise ImportError("nuScenes dependencies not available. Install nuscenes-devkit for original functionality.")

    nusc = NuScenes(version='v1.0-{}'.format(version),
                    dataroot=os.path.join(dataroot, version),
                    verbose=False)
    parser = {
        'vizdata': VizData,
        'segmentationdata': SegmentationData,
    }[parser_name]
    traindata = parser(nusc, is_train=True, data_aug_conf=data_aug_conf,
                         grid_conf=grid_conf)
    valdata = parser(nusc, is_train=False, data_aug_conf=data_aug_conf,
                       grid_conf=grid_conf)

    trainloader = torch.utils.data.DataLoader(traindata, batch_size=bsz,
                                              shuffle=True,
                                              num_workers=nworkers,
                                              drop_last=True,
                                              worker_init_fn=worker_rnd_init)
    valloader = torch.utils.data.DataLoader(valdata, batch_size=bsz,
                                            shuffle=False,
                                            num_workers=nworkers)

    return trainloader, valloader


def compile_dashcam_data(video_path, data_aug_conf, grid_conf, bsz=1,
                        nworkers=0, camera_params=None, frame_skip=1):
    """
    Create data loader for dashcam footage

    Args:
        video_path: Path to dashcam video file
        data_aug_conf: Data augmentation configuration
        grid_conf: Grid configuration for BEV
        bsz: Batch size
        nworkers: Number of workers
        camera_params: Camera calibration parameters
        frame_skip: Skip frames to reduce processing
    """
    dataset = DashcamData(video_path, data_aug_conf, grid_conf,
                         camera_params, frame_skip)

    dataloader = torch.utils.data.DataLoader(dataset, batch_size=bsz,
                                            shuffle=False,
                                            num_workers=nworkers)

    return dataloader
