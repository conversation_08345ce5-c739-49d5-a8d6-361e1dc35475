# Single Dashcam 3D Road Reconstruction - Quick Start Guide

This system is specifically designed for **single-angle dashcam footage** and will create a 3D model of the road area from your video.

## ✅ System Status: READY
Your environment is set up and ready to process single dashcam videos!

## 🚗 How to Use with Your Single Dashcam Video

### Step 1: Prepare Your Video
1. Place your dashcam video file in this directory
2. Rename it to something simple like `my_dashcam.mp4`
3. Supported formats: MP4, AVI, MOV, MKV

### Step 2: Basic Processing (Recommended)
```bash
# Activate the virtual environment (if not already active)
.\venv\Scripts\activate

# Process your dashcam video
python lift-splat-shoot-master/dashcam_inference.py my_dashcam.mp4 --output results
```

### Step 3: View Your 3D Model
After processing, check the `results/` folder for:
- `road_model.ply` - 3D model (open with MeshLab, Blender, or online PLY viewers)
- `road_model.obj` - 3D model (open with any 3D software)
- `3d_visualization.png` - Preview image of your 3D model
- `bev_maps/` - Bird's-eye-view images of the road

## 🎛️ Customization Options

### For Different Video Qualities
```bash
# High quality video (process more frames)
python lift-splat-shoot-master/dashcam_inference.py my_dashcam.mp4 --frame-skip 5 --max-frames 200

# Quick processing (fewer frames)
python lift-splat-shoot-master/dashcam_inference.py my_dashcam.mp4 --frame-skip 20 --max-frames 50

# Highway driving (longer range)
python lift-splat-shoot-master/dashcam_inference.py my_dashcam.mp4 --output highway_results
```

### For Better Accuracy (Optional)
If you want better results, you can calibrate your specific dashcam:

```bash
# Create camera configuration for your dashcam
python -c "
import sys; sys.path.append('lift-splat-shoot-master/src')
from camera_calibration import create_calibration_config
create_calibration_config('my_dashcam.mp4', 'my_camera_config.json')
"

# Use the calibration
python lift-splat-shoot-master/dashcam_inference.py my_dashcam.mp4 --camera-config my_camera_config.json
```

## 📊 What You'll Get

### 3D Model Files
- **PLY format**: Best for scientific/technical use
- **OBJ format**: Compatible with most 3D software
- **Point cloud**: Represents road surface and nearby objects

### Visualizations
- **Bird's-eye-view maps**: Top-down view of detected road areas
- **3D visualization**: Preview of the reconstructed scene
- **Frame-by-frame analysis**: Individual processed frames

### Coverage Area
- **Forward**: 0-50 meters ahead of the car
- **Sideways**: ±25 meters left/right of the car
- **Height**: Road surface with small variations

## 🔧 Troubleshooting

### "Could not open video file"
- Check the video file path and name
- Make sure the file isn't corrupted
- Try a different video format

### "No 3D points generated"
- Your video might be too dark or unclear
- Try lowering the threshold: add `--threshold 0.2` to the command
- Make sure the camera is pointing at the road

### Slow processing
- Use `--frame-skip 15` to process fewer frames
- Reduce `--max-frames 30` for quick tests
- Close other applications to free up memory

### Poor quality results
- Use a higher resolution dashcam video (1080p+)
- Ensure good lighting conditions
- Make sure the camera is stable and clean

## 📝 Example Commands

```bash
# Basic usage
python lift-splat-shoot-master/dashcam_inference.py dashcam_video.mp4

# Custom output folder
python lift-splat-shoot-master/dashcam_inference.py dashcam_video.mp4 --output my_results

# Quick test (fast processing)
python lift-splat-shoot-master/dashcam_inference.py dashcam_video.mp4 --frame-skip 30 --max-frames 20

# High quality (slow but detailed)
python lift-splat-shoot-master/dashcam_inference.py dashcam_video.mp4 --frame-skip 3 --max-frames 300

# With custom camera settings
python lift-splat-shoot-master/dashcam_inference.py dashcam_video.mp4 --camera-config camera.json
```

## 🎯 Tips for Best Results

### Video Quality
- **Resolution**: 1280x720 minimum, 1920x1080 preferred
- **Lighting**: Daylight works best, avoid night footage
- **Stability**: Ensure dashcam is firmly mounted
- **Cleanliness**: Clean camera lens before recording

### Driving Conditions
- **Road type**: Works best on highways and main roads
- **Traffic**: Less crowded roads give cleaner results
- **Weather**: Clear weather conditions preferred
- **Speed**: Moderate speeds (30-60 mph) work well

### Processing Settings
- Start with default settings
- For quick tests: `--frame-skip 20 --max-frames 30`
- For final results: `--frame-skip 5 --max-frames 200`
- Adjust based on your computer's speed and video length

## 🚀 Next Steps

1. **Process your video** with the basic command
2. **Open the 3D model** in a viewer like MeshLab or Blender
3. **Experiment** with different settings for better results
4. **Try different sections** of your dashcam footage

## 📁 File Structure After Processing
```
results/
├── road_model.ply              # 3D model (PLY format)
├── road_model.obj              # 3D model (OBJ format)
├── 3d_visualization.png        # 3D preview image
├── 3d_model_metadata.json      # Technical details
├── processing_summary.json     # Processing info
├── camera_config.json          # Camera parameters used
└── bev_maps/                   # Bird's-eye-view images
    ├── bev_frame_0000.png
    ├── bev_frame_0001.png
    └── ...
```

## 🆘 Need Help?
- Check that your video file is accessible and not corrupted
- Make sure you're in the correct directory with the virtual environment activated
- Try with a shorter video clip first (30 seconds - 2 minutes)
- The system works best with forward-facing dashcam footage showing clear road areas

**Ready to create your 3D road model? Just run the command with your dashcam video file!**
