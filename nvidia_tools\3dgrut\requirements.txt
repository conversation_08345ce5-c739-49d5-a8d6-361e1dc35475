plyfile
torchmetrics
tensorboard
fire
omegaconf
hydra-core
scikit-learn
wandb
polyscope>=2.3.0
addict
rich
slangtorch==1.3.4
# NeRF Dataset requirements
kornia
opencv-python
einops
imageio
msgpack
dataclasses_json
# JIT compilation
setuptools <72.1.0
# Fused-ssim
git+https://github.com/rahul-goel/fused-ssim@1272e21a282342e89537159e4bad508b19b34157
# Playground
tqdm
libigl
pygltflib
# --find-links https://nvidia-kaolin.s3.us-east-2.amazonaws.com/torch-2.1.2_cu118.html
# kaolin==0.17.0
usd-core
