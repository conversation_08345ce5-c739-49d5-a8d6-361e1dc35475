#!/usr/bin/env python3
# Example usage of the 3D reconstruction pipeline

# Basic usage:
# python dashcam_to_3d_pipeline.py your_video.mp4

# Advanced usage:
# python dashcam_to_3d_pipeline.py your_video.mp4 \
#     --output-dir my_3d_results \
#     --frame-skip 5 \
#     --max-frames 300

# The pipeline will:
# 1. Process your dashcam video with Lift-Splat-Shoot
# 2. Convert the output to point clouds
# 3. Use NKSR for high-quality surface reconstruction
# 4. Generate a final 3D model in PLY format

print("Example commands:")
print("1. Basic reconstruction:")
print("   python dashcam_to_3d_pipeline.py DashCam.mp4")
print()
print("2. High-quality reconstruction (more frames, less skipping):")
print("   python dashcam_to_3d_pipeline.py DashCam.mp4 --frame-skip 3 --max-frames 500")
print()
print("3. Quick test (fewer frames):")
print("   python dashcam_to_3d_pipeline.py DashCam.mp4 --frame-skip 20 --max-frames 50")
