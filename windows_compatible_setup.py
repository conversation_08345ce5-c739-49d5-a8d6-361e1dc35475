#!/usr/bin/env python3
"""
Windows-Compatible Setup Script for Enhanced Dashcam to PLY Pipeline
Fixes Unicode encoding issues on Windows
"""

import os
import sys
import subprocess
import platform
import json
from pathlib import Path

def print_status(message, status="INFO"):
    """Print status message with Windows-compatible formatting"""
    status_symbols = {
        "OK": "[OK]",
        "ERROR": "[ERROR]", 
        "WARNING": "[WARNING]",
        "INFO": "[INFO]"
    }
    print(f"{status_symbols.get(status, '[INFO]')} {message}")

def create_virtual_environment():
    """Create virtual environment"""
    print_status("Creating Enhanced Virtual Environment", "INFO")
    
    venv_name = "venv_enhanced_3d"
    
    if os.path.exists(venv_name):
        print_status(f"Virtual environment {venv_name} already exists", "OK")
        return venv_name
    
    print_status(f"Creating virtual environment: {venv_name}", "INFO")
    result = subprocess.run([sys.executable, '-m', 'venv', venv_name])
    
    if result.returncode == 0:
        print_status("Virtual environment created successfully", "OK")
    else:
        print_status("Failed to create virtual environment", "ERROR")
        return None
    
    return venv_name

def get_python_executable(venv_path):
    """Get python executable path for the virtual environment"""
    if platform.system() == "Windows":
        return os.path.join(venv_path, "Scripts", "python.exe")
    else:
        return os.path.join(venv_path, "bin", "python")

def install_core_dependencies(venv_path):
    """Install core dependencies with better error handling"""
    print_status("Installing Core Dependencies", "INFO")
    
    python_exe = get_python_executable(venv_path)
    
    if not os.path.exists(python_exe):
        print_status(f"Python executable not found: {python_exe}", "ERROR")
        return False
    
    # Upgrade pip first
    print_status("Upgrading pip...", "INFO")
    result = subprocess.run([python_exe, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                          capture_output=True, text=True)
    if result.returncode != 0:
        print_status(f"Failed to upgrade pip: {result.stderr}", "WARNING")
    
    # Install PyTorch with CUDA
    print_status("Installing PyTorch with CUDA...", "INFO")
    torch_cmd = [
        python_exe, '-m', 'pip', 'install', 
        'torch', 'torchvision', 'torchaudio',
        '--index-url', 'https://download.pytorch.org/whl/cu118'
    ]
    
    result = subprocess.run(torch_cmd, capture_output=True, text=True)
    if result.returncode == 0:
        print_status("PyTorch installed successfully", "OK")
    else:
        print_status(f"PyTorch installation failed: {result.stderr}", "ERROR")
        return False
    
    # Install other dependencies
    dependencies = [
        'open3d>=0.17.0',
        'opencv-python',
        'matplotlib',
        'numpy',
        'Pillow',
        'trimesh',
        'plyfile',
        'scikit-image',
        'scipy',
        'tqdm'
    ]
    
    print_status("Installing 3D processing libraries...", "INFO")
    for dep in dependencies:
        print_status(f"Installing {dep}...", "INFO")
        result = subprocess.run([python_exe, '-m', 'pip', 'install', dep], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print_status(f"{dep} installed", "OK")
        else:
            print_status(f"Failed to install {dep}: {result.stderr}", "WARNING")
    
    # Install NKSR
    print_status("Installing NKSR...", "INFO")
    nksr_cmd = [
        python_exe, '-m', 'pip', 'install', 'nksr',
        '-f', 'https://nksr.huangjh.tech/whl/torch-2.0.0+cu118.html'
    ]
    
    result = subprocess.run(nksr_cmd, capture_output=True, text=True)
    if result.returncode == 0:
        print_status("NKSR installed successfully", "OK")
    else:
        print_status(f"NKSR installation failed: {result.stderr}", "WARNING")
        print_status("You may need to install NKSR manually later", "INFO")
    
    return True

def test_installation_simple(venv_path):
    """Simple installation test without Unicode characters"""
    print_status("Testing Installation", "INFO")
    
    python_exe = get_python_executable(venv_path)
    
    # Simple test script
    test_script = '''
import sys
try:
    import torch
    print("PyTorch:", torch.__version__)
    print("CUDA available:", torch.cuda.is_available())
    if torch.cuda.is_available():
        print("CUDA device:", torch.cuda.get_device_name())
except ImportError as e:
    print("PyTorch import failed:", e)

try:
    import cv2
    print("OpenCV:", cv2.__version__)
except ImportError as e:
    print("OpenCV import failed:", e)

try:
    import open3d as o3d
    print("Open3D:", o3d.__version__)
except ImportError as e:
    print("Open3D import failed:", e)

try:
    import nksr
    print("NKSR: Available")
except ImportError as e:
    print("NKSR: Not available -", e)

print("Test completed successfully!")
'''
    
    try:
        result = subprocess.run([python_exe, '-c', test_script], 
                              capture_output=True, text=True, encoding='utf-8', errors='replace')
        
        if result.returncode == 0:
            print_status("Installation test passed!", "OK")
            print(result.stdout)
            return True
        else:
            print_status("Installation test failed!", "ERROR")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
    except Exception as e:
        print_status(f"Test failed with exception: {e}", "ERROR")
        return False

def create_batch_files(venv_path):
    """Create Windows batch files for easy activation"""
    print_status("Creating Windows batch files...", "INFO")
    
    # Activation batch file
    activate_content = f'''@echo off
echo Activating Enhanced 3D Pipeline Environment...
call "{venv_path}\\Scripts\\activate.bat"
echo Environment activated! You can now run:
echo   python enhanced_dashcam_to_ply.py your_video.mp4
cmd /k
'''
    
    with open("activate_pipeline.bat", "w") as f:
        f.write(activate_content)
    
    print_status("Created activate_pipeline.bat", "OK")
    
    # Quick test batch file
    test_content = f'''@echo off
echo Running Enhanced Pipeline Test...
call "{venv_path}\\Scripts\\activate.bat"
python test_enhanced_pipeline.py
pause
'''
    
    with open("test_pipeline.bat", "w") as f:
        f.write(test_content)
    
    print_status("Created test_pipeline.bat", "OK")

def main():
    print("Enhanced Dashcam to PLY Pipeline Setup (Windows Compatible)")
    print("=" * 60)
    
    # Check prerequisites
    if not os.path.exists("lift-splat-shoot-master"):
        print_status("lift-splat-shoot-master directory not found!", "ERROR")
        print_status("Please ensure you have the lift-splat-shoot-master directory", "INFO")
        return
    
    # Create virtual environment
    venv_path = create_virtual_environment()
    if not venv_path:
        return
    
    # Install dependencies
    if not install_core_dependencies(venv_path):
        print_status("Dependency installation failed", "ERROR")
        return
    
    # Test installation
    if not test_installation_simple(venv_path):
        print_status("Installation test failed. Please check the errors above.", "WARNING")
        print_status("You may still be able to use the pipeline", "INFO")
    
    # Create Windows batch files
    if platform.system() == "Windows":
        create_batch_files(venv_path)
    
    print("\n" + "=" * 60)
    print_status("Setup Complete!", "OK")
    print("=" * 60)
    
    print(f"\nNext Steps:")
    if platform.system() == "Windows":
        print(f"1. Double-click 'activate_pipeline.bat' to activate environment")
        print(f"2. Or manually activate:")
        print(f"   {venv_path}\\Scripts\\activate")
    else:
        print(f"1. Activate the environment:")
        print(f"   source {venv_path}/bin/activate")
    
    print(f"\n3. Run the enhanced pipeline:")
    print(f"   python enhanced_dashcam_to_ply.py your_video.mp4")
    
    print(f"\n4. Or test with sample video:")
    sample_video = "lift-splat-shoot-master/DashCam.mp4"
    if os.path.exists(sample_video):
        print(f"   python enhanced_dashcam_to_ply.py {sample_video}")
    
    print(f"\nFor help:")
    print(f"   python enhanced_dashcam_to_ply.py --help")

if __name__ == "__main__":
    main()
