name: nksr
channels:
  - pyg
  - nvidia/label/cuda-11.8.0
  - pytorch
  - conda-forge
dependencies:
  - python=3.10                   # Fix version for reproducibility
  - pytorch=2.0.0                 # |
  - pytorch-cuda=11.8
  - pytorch-lightning=1.9.4       # |
  - libprotobuf=3.19.6    # Protobuf
  - protobuf=3.19.6       # | 4.x has weird linking bugs...
  - mkl=2023.2.0
  - numpy<2.0.0
  - tensorboard
  - wandb
  - pybind11
  - pip
  - gitpython
  - ca-certificates
  - certifi
  - openssl
  - cuda-toolkit
  - cuda-cudart
  - cuda-nvcc
  - cuda-tools
  - libcusparse
  - parameterized
  - gcc_linux-64=11
  - gxx_linux-64=11
  - cuda-toolkit
  - setuptools
  - cmake
  - ninja
  - ipython
  - matplotlib
  - tqdm
  - pyg
  - rich
  - pandas
  - pytorch-scatter
  - omegaconf
  - flatten-dict
  - pyntcloud
  - pip:
    - -f https://pycg.huangjh.tech/packages/index.html
    - python-pycg[full]==0.5.2
    - randomname
    - pykdtree
    - plyfile
